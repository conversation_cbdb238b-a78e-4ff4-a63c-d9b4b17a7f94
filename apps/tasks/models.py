import uuid
from django.db import models
from django.contrib.contenttypes.models import ContentType
from django.contrib.contenttypes.fields import GenericForeignKey
from apps.projects.models import Project
from apps.team.models import TeamMember


class Task(models.Model):
    """
    Represents individual tasks within projects, including AI-generated tasks
    with reasoning, dependencies, and assignment tracking.
    """
    PRIORITY_CHOICES = [
        ('LOW', 'Low'),
        ('MEDIUM', 'Medium'),
        ('HIGH', 'High'),
        ('CRITICAL', 'Critical'),
    ]

    TASK_STATUS_CHOICES = [
        ('TODO', 'To Do'),
        ('IN_PROGRESS', 'In Progress'),
        ('IN_REVIEW', 'In Review'),
        ('BLOCKED', 'Blocked'),
        ('COMPLETED', 'Completed'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    project = models.ForeignKey(Project, on_delete=models.CASCADE)
    parent_task = models.ForeignKey('self', null=True, blank=True, on_delete=models.CASCADE, related_name='subtasks')
    title = models.CharField(max_length=200)
    description = models.TextField()
    ai_generated = models.<PERSON>oleanField(default=False)
    ai_reasoning = models.TextField(null=True, blank=True)  # AI explanation for task creation
    priority = models.CharField(max_length=20, choices=PRIORITY_CHOICES)
    status = models.CharField(max_length=20, choices=TASK_STATUS_CHOICES, default='TODO')
    estimated_hours = models.DecimalField(max_digits=6, decimal_places=2)
    actual_hours = models.DecimalField(max_digits=6, decimal_places=2, default=0)
    complexity_score = models.IntegerField(default=1)  # 1-10 scale
    required_skills = models.JSONField(default=list)
    dependencies = models.ManyToManyField('self', symmetrical=False, blank=True, related_name='dependent_tasks')
    start_date = models.DateTimeField(null=True, blank=True)
    due_date = models.DateTimeField(null=True, blank=True)
    completion_date = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'tasks_task'
        verbose_name = 'Task'
        verbose_name_plural = 'Tasks'
        indexes = [
            models.Index(fields=['project', 'status']),
            models.Index(fields=['priority', 'status']),
            models.Index(fields=['due_date']),
            models.Index(fields=['created_at']),
            models.Index(fields=['ai_generated']),
        ]

    def __str__(self):
        return self.title

    @property
    def progress_percentage(self):
        """Calculate task progress based on actual vs estimated hours."""
        if self.estimated_hours > 0:
            return min(100, (self.actual_hours / self.estimated_hours) * 100)
        return 0 if self.status != 'COMPLETED' else 100

    @property
    def is_overdue(self):
        """Check if task is overdue."""
        if self.due_date and self.status not in ['COMPLETED', 'CANCELLED']:
            from django.utils import timezone
            return timezone.now() > self.due_date
        return False


class TaskAssignment(models.Model):
    """
    Represents the assignment of tasks to team members with tracking
    of assignment reasoning, progress, and completion status.
    """
    ASSIGNMENT_STATUS_CHOICES = [
        ('ASSIGNED', 'Assigned'),
        ('ACCEPTED', 'Accepted'),
        ('IN_PROGRESS', 'In Progress'),
        ('COMPLETED', 'Completed'),
        ('REJECTED', 'Rejected'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    task = models.ForeignKey(Task, on_delete=models.CASCADE)
    team_member = models.ForeignKey(TeamMember, on_delete=models.CASCADE)
    assigned_by = models.CharField(max_length=50)  # AI PM, CEO, etc.
    assignment_reason = models.TextField()  # AI explanation for assignment
    assigned_at = models.DateTimeField(auto_now_add=True)
    started_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    status = models.CharField(max_length=20, choices=ASSIGNMENT_STATUS_CHOICES, default='ASSIGNED')
    progress_percentage = models.IntegerField(default=0)
    notes = models.TextField(blank=True)

    class Meta:
        db_table = 'tasks_assignment'
        verbose_name = 'Task Assignment'
        verbose_name_plural = 'Task Assignments'
        unique_together = ['task', 'team_member']
        indexes = [
            models.Index(fields=['task', 'status']),
            models.Index(fields=['team_member', 'status']),
            models.Index(fields=['assigned_at']),
        ]

    def __str__(self):
        return f"{self.task.title} → {self.team_member.name}"


class WorkflowState(models.Model):
    """
    Generic model to track workflow states for projects, tasks, and other entities
    with AI decision logging and state transition reasoning.
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE)
    object_id = models.UUIDField()
    content_object = GenericForeignKey('content_type', 'object_id')
    state_name = models.CharField(max_length=50)
    state_data = models.JSONField(default=dict)
    ai_decision_log = models.JSONField(default=list)  # Track AI decisions
    transition_reason = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'tasks_workflow_state'
        verbose_name = 'Workflow State'
        verbose_name_plural = 'Workflow States'
        indexes = [
            models.Index(fields=['content_type', 'object_id']),
            models.Index(fields=['state_name']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f"{self.content_object} - {self.state_name}"
