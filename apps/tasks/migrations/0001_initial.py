# Generated by Django 5.2.4 on 2025-07-06 10:42

import django.db.models.deletion
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("contenttypes", "0002_remove_content_type_name"),
        ("projects", "0001_initial"),
        ("team", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="Task",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("title", models.CharField(max_length=200)),
                ("description", models.TextField()),
                ("ai_generated", models.BooleanField(default=False)),
                ("ai_reasoning", models.TextField(blank=True, null=True)),
                (
                    "priority",
                    models.CharField(
                        choices=[
                            ("LOW", "Low"),
                            ("MEDIUM", "Medium"),
                            ("HIGH", "High"),
                            ("CRITICAL", "Critical"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "status",
                    models.<PERSON>r<PERSON><PERSON>(
                        choices=[
                            ("TODO", "To Do"),
                            ("IN_PROGRESS", "In Progress"),
                            ("IN_REVIEW", "In Review"),
                            ("BLOCKED", "Blocked"),
                            ("COMPLETED", "Completed"),
                        ],
                        default="TODO",
                        max_length=20,
                    ),
                ),
                (
                    "estimated_hours",
                    models.DecimalField(decimal_places=2, max_digits=6),
                ),
                (
                    "actual_hours",
                    models.DecimalField(decimal_places=2, default=0, max_digits=6),
                ),
                ("complexity_score", models.IntegerField(default=1)),
                ("required_skills", models.JSONField(default=list)),
                ("start_date", models.DateTimeField(blank=True, null=True)),
                ("due_date", models.DateTimeField(blank=True, null=True)),
                ("completion_date", models.DateTimeField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "dependencies",
                    models.ManyToManyField(
                        blank=True, related_name="dependent_tasks", to="tasks.task"
                    ),
                ),
                (
                    "parent_task",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="subtasks",
                        to="tasks.task",
                    ),
                ),
                (
                    "project",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="projects.project",
                    ),
                ),
            ],
            options={
                "verbose_name": "Task",
                "verbose_name_plural": "Tasks",
                "db_table": "tasks_task",
            },
        ),
        migrations.CreateModel(
            name="TaskAssignment",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("assigned_by", models.CharField(max_length=50)),
                ("assignment_reason", models.TextField()),
                ("assigned_at", models.DateTimeField(auto_now_add=True)),
                ("started_at", models.DateTimeField(blank=True, null=True)),
                ("completed_at", models.DateTimeField(blank=True, null=True)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("ASSIGNED", "Assigned"),
                            ("ACCEPTED", "Accepted"),
                            ("IN_PROGRESS", "In Progress"),
                            ("COMPLETED", "Completed"),
                            ("REJECTED", "Rejected"),
                        ],
                        default="ASSIGNED",
                        max_length=20,
                    ),
                ),
                ("progress_percentage", models.IntegerField(default=0)),
                ("notes", models.TextField(blank=True)),
                (
                    "task",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="tasks.task"
                    ),
                ),
                (
                    "team_member",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="team.teammember",
                    ),
                ),
            ],
            options={
                "verbose_name": "Task Assignment",
                "verbose_name_plural": "Task Assignments",
                "db_table": "tasks_assignment",
            },
        ),
        migrations.CreateModel(
            name="WorkflowState",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("object_id", models.UUIDField()),
                ("state_name", models.CharField(max_length=50)),
                ("state_data", models.JSONField(default=dict)),
                ("ai_decision_log", models.JSONField(default=list)),
                ("transition_reason", models.TextField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "content_type",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="contenttypes.contenttype",
                    ),
                ),
            ],
            options={
                "verbose_name": "Workflow State",
                "verbose_name_plural": "Workflow States",
                "db_table": "tasks_workflow_state",
            },
        ),
        migrations.AddIndex(
            model_name="task",
            index=models.Index(
                fields=["project", "status"], name="tasks_task_project_b78682_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="task",
            index=models.Index(
                fields=["priority", "status"], name="tasks_task_priorit_685c61_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="task",
            index=models.Index(
                fields=["due_date"], name="tasks_task_due_dat_bce847_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="task",
            index=models.Index(
                fields=["created_at"], name="tasks_task_created_be1ba2_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="task",
            index=models.Index(
                fields=["ai_generated"], name="tasks_task_ai_gene_ae0989_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="taskassignment",
            index=models.Index(
                fields=["task", "status"], name="tasks_assig_task_id_61cb68_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="taskassignment",
            index=models.Index(
                fields=["team_member", "status"], name="tasks_assig_team_me_05ab41_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="taskassignment",
            index=models.Index(
                fields=["assigned_at"], name="tasks_assig_assigne_d51dce_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="taskassignment",
            unique_together={("task", "team_member")},
        ),
        migrations.AddIndex(
            model_name="workflowstate",
            index=models.Index(
                fields=["content_type", "object_id"],
                name="tasks_workf_content_88b9c9_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="workflowstate",
            index=models.Index(
                fields=["state_name"], name="tasks_workf_state_n_e5017b_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="workflowstate",
            index=models.Index(
                fields=["created_at"], name="tasks_workf_created_1825c3_idx"
            ),
        ),
    ]
