# Generated by Django 5.2.4 on 2025-07-06 10:42

import django.db.models.deletion
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="AIRole",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("name", models.CharField(max_length=50)),
                ("description", models.TextField()),
                ("system_prompt", models.TextField()),
                ("capabilities", models.JSONField(default=list)),
                ("decision_authority", models.J<PERSON><PERSON>ield(default=dict)),
                ("interaction_style", models.Char<PERSON>ield(max_length=50)),
                (
                    "experience_level",
                    models.CharField(
                        choices=[
                            ("JUNIOR", "Junior"),
                            ("MID", "Mid-Level"),
                            ("SENIOR", "Senior"),
                            ("LEAD", "Lead"),
                        ],
                        max_length=20,
                    ),
                ),
                ("hourly_rate", models.DecimalField(decimal_places=2, max_digits=8)),
                ("is_active", models.<PERSON><PERSON><PERSON><PERSON>ield(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name": "AI Role",
                "verbose_name_plural": "AI Roles",
                "db_table": "team_ai_role",
            },
        ),
        migrations.CreateModel(
            name="TeamMember",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("name", models.CharField(max_length=100)),
                ("specialization", models.CharField(max_length=100)),
                ("skills", models.JSONField(default=list)),
                (
                    "availability_hours",
                    models.DecimalField(decimal_places=1, default=40, max_digits=4),
                ),
                (
                    "current_workload",
                    models.DecimalField(decimal_places=1, default=0, max_digits=4),
                ),
                (
                    "performance_rating",
                    models.DecimalField(decimal_places=2, default=5.0, max_digits=3),
                ),
                ("hire_date", models.DateTimeField(auto_now_add=True)),
                ("is_active", models.BooleanField(default=True)),
                ("ai_context", models.JSONField(default=dict)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "ai_role",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="team.airole"
                    ),
                ),
            ],
            options={
                "verbose_name": "Team Member",
                "verbose_name_plural": "Team Members",
                "db_table": "team_member",
                "indexes": [
                    models.Index(
                        fields=["ai_role", "is_active"],
                        name="team_member_ai_role_326f6b_idx",
                    ),
                    models.Index(
                        fields=["current_workload"],
                        name="team_member_current_c24c8e_idx",
                    ),
                ],
            },
        ),
    ]
