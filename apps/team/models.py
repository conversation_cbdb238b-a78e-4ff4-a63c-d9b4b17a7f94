import uuid
from django.db import models
from django.contrib.contenttypes.models import ContentType
from django.contrib.contenttypes.fields import GenericForeignKey


class AIRole(models.Model):
    """
    Defines different AI roles (Product Manager, Developer, Designer, etc.)
    with their capabilities, decision authority, and behavioral characteristics.
    """
    EXPERIENCE_CHOICES = [
        ('JUNIOR', 'Junior'),
        ('MID', 'Mid-Level'),
        ('SENI<PERSON>', 'Senior'),
        ('LEAD', 'Lead'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=50)  # e.g., "Product Manager", "Developer"
    description = models.TextField()
    system_prompt = models.TextField()  # Base prompt for this role
    capabilities = models.JSONField(default=list)  # List of capabilities
    decision_authority = models.J<PERSON><PERSON>ield(default=dict)  # What this role can decide
    interaction_style = models.Char<PERSON>ield(max_length=50)  # Communication style
    experience_level = models.Char<PERSON><PERSON>(max_length=20, choices=EXPERIENCE_CHOICES)
    hourly_rate = models.DecimalField(max_digits=8, decimal_places=2)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'team_ai_role'
        verbose_name = 'AI Role'
        verbose_name_plural = 'AI Roles'

    def __str__(self):
        return f"{self.name} ({self.experience_level})"


class TeamMember(models.Model):
    """
    Represents individual AI team members with their skills, availability,
    and performance metrics. Each team member is based on an AI role.
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    ai_role = models.ForeignKey(AIRole, on_delete=models.CASCADE)
    name = models.CharField(max_length=100)
    specialization = models.CharField(max_length=100)
    skills = models.JSONField(default=list)
    availability_hours = models.DecimalField(max_digits=4, decimal_places=1, default=40)
    current_workload = models.DecimalField(max_digits=4, decimal_places=1, default=0)
    performance_rating = models.DecimalField(max_digits=3, decimal_places=2, default=5.0)
    hire_date = models.DateTimeField(auto_now_add=True)
    is_active = models.BooleanField(default=True)
    ai_context = models.JSONField(default=dict)  # Persistent AI memory/context
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'team_member'
        verbose_name = 'Team Member'
        verbose_name_plural = 'Team Members'
        indexes = [
            models.Index(fields=['ai_role', 'is_active']),
            models.Index(fields=['current_workload']),
        ]

    def __str__(self):
        return f"{self.name} - {self.ai_role.name}"

    @property
    def workload_percentage(self):
        """Calculate current workload as percentage of availability."""
        if self.availability_hours > 0:
            return (self.current_workload / self.availability_hours) * 100
        return 0

    @property
    def available_hours(self):
        """Calculate remaining available hours."""
        return max(0, self.availability_hours - self.current_workload)
