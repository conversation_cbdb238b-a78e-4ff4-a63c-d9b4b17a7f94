import uuid
from django.db import models
from django.conf import settings
from apps.projects.models import Project
from apps.team.models import TeamMember


class AIConversation(models.Model):
    """
    Tracks AI conversations and context for different AI roles and projects.
    Maintains conversation history and context for consistent AI behavior.
    """
    CONVERSATION_TYPE_CHOICES = [
        ('REQUIREMENT_ANALYSIS', 'Requirement Analysis'),
        ('TASK_GENERATION', 'Task Generation'),
        ('TEAM_PLANNING', 'Team Planning'),
        ('PROGRESS_REVIEW', 'Progress Review'),
        ('HEADCOUNT_REQUEST', 'Headcount Request'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    project = models.ForeignKey(Project, on_delete=models.CASCADE, null=True, blank=True)
    team_member = models.ForeignKey(TeamMember, on_delete=models.CASCADE, null=True, blank=True)
    conversation_type = models.Char<PERSON>ield(max_length=50, choices=CONVERSATION_TYPE_CHOICES)
    context_data = models.J<PERSON><PERSON><PERSON>(default=dict)  # Conversation context and state
    messages = models.J<PERSON><PERSON>ield(default=list)  # Conversation history
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'ai_service_conversation'
        verbose_name = 'AI Conversation'
        verbose_name_plural = 'AI Conversations'
        indexes = [
            models.Index(fields=['project', 'conversation_type']),
            models.Index(fields=['team_member', 'is_active']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        if self.project:
            return f"{self.conversation_type} - {self.project.title}"
        return f"{self.conversation_type} - {self.team_member.name if self.team_member else 'General'}"


class AIDecision(models.Model):
    """
    Logs AI decisions with reasoning, confidence scores, and outcomes
    for audit trails and learning purposes.
    """
    DECISION_TYPE_CHOICES = [
        ('TASK_ASSIGNMENT', 'Task Assignment'),
        ('PRIORITY_SETTING', 'Priority Setting'),
        ('RESOURCE_ALLOCATION', 'Resource Allocation'),
        ('TIMELINE_ESTIMATION', 'Timeline Estimation'),
        ('HEADCOUNT_REQUEST', 'Headcount Request'),
        ('WORKFLOW_TRANSITION', 'Workflow Transition'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    conversation = models.ForeignKey(AIConversation, on_delete=models.CASCADE, null=True, blank=True)
    decision_type = models.CharField(max_length=50, choices=DECISION_TYPE_CHOICES)
    decision_data = models.JSONField(default=dict)  # The actual decision made
    reasoning = models.TextField()  # AI explanation for the decision
    confidence_score = models.DecimalField(max_digits=3, decimal_places=2)  # 0.00 to 1.00
    alternatives_considered = models.JSONField(default=list)  # Other options considered
    outcome_data = models.JSONField(default=dict, null=True, blank=True)  # Actual outcome
    feedback_score = models.DecimalField(max_digits=3, decimal_places=2, null=True, blank=True)  # Human feedback
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'ai_service_decision'
        verbose_name = 'AI Decision'
        verbose_name_plural = 'AI Decisions'
        indexes = [
            models.Index(fields=['decision_type', 'confidence_score']),
            models.Index(fields=['created_at']),
            models.Index(fields=['feedback_score']),
        ]

    def __str__(self):
        return f"{self.decision_type} - {self.confidence_score}"


class AIMetrics(models.Model):
    """
    Tracks AI performance metrics and analytics for different roles and tasks.
    Used for monitoring AI effectiveness and improvement opportunities.
    """
    METRIC_TYPE_CHOICES = [
        ('ACCURACY', 'Decision Accuracy'),
        ('EFFICIENCY', 'Task Efficiency'),
        ('SATISFACTION', 'User Satisfaction'),
        ('COMPLETION_RATE', 'Task Completion Rate'),
        ('ESTIMATION_ACCURACY', 'Time Estimation Accuracy'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    team_member = models.ForeignKey(TeamMember, on_delete=models.CASCADE, null=True, blank=True)
    project = models.ForeignKey(Project, on_delete=models.CASCADE, null=True, blank=True)
    metric_type = models.CharField(max_length=50, choices=METRIC_TYPE_CHOICES)
    metric_value = models.DecimalField(max_digits=10, decimal_places=4)
    metric_data = models.JSONField(default=dict)  # Additional metric details
    measurement_period_start = models.DateTimeField()
    measurement_period_end = models.DateTimeField()
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'ai_service_metrics'
        verbose_name = 'AI Metrics'
        verbose_name_plural = 'AI Metrics'
        indexes = [
            models.Index(fields=['team_member', 'metric_type']),
            models.Index(fields=['project', 'metric_type']),
            models.Index(fields=['measurement_period_start', 'measurement_period_end']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f"{self.metric_type}: {self.metric_value}"


class AIPromptTemplate(models.Model):
    """
    Stores and manages AI prompt templates for different roles and scenarios.
    Allows for dynamic prompt generation and A/B testing of prompts.
    """
    TEMPLATE_TYPE_CHOICES = [
        ('SYSTEM', 'System Prompt'),
        ('TASK_ANALYSIS', 'Task Analysis'),
        ('REQUIREMENT_REVIEW', 'Requirement Review'),
        ('TEAM_ASSESSMENT', 'Team Assessment'),
        ('PROGRESS_UPDATE', 'Progress Update'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=100)
    template_type = models.CharField(max_length=50, choices=TEMPLATE_TYPE_CHOICES)
    role_specific = models.CharField(max_length=50, null=True, blank=True)  # Specific AI role
    template_content = models.TextField()
    variables = models.JSONField(default=list)  # Template variables
    version = models.CharField(max_length=20, default='1.0')
    is_active = models.BooleanField(default=True)
    usage_count = models.IntegerField(default=0)
    success_rate = models.DecimalField(max_digits=5, decimal_places=2, default=0.0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'ai_service_prompt_template'
        verbose_name = 'AI Prompt Template'
        verbose_name_plural = 'AI Prompt Templates'
        indexes = [
            models.Index(fields=['template_type', 'is_active']),
            models.Index(fields=['role_specific', 'is_active']),
            models.Index(fields=['success_rate']),
        ]

    def __str__(self):
        return f"{self.name} v{self.version}"
