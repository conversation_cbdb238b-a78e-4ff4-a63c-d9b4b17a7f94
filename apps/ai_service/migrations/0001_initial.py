# Generated by Django 5.2.4 on 2025-07-06 10:42

import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="AIConversation",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "conversation_type",
                    models.CharField(
                        choices=[
                            ("REQUIREMENT_ANALYSIS", "Requirement Analysis"),
                            ("TASK_GENERATION", "Task Generation"),
                            ("TEAM_PLANNING", "Team Planning"),
                            ("PROGRESS_REVIEW", "Progress Review"),
                            ("HEADCOUNT_REQUEST", "Headcount Request"),
                        ],
                        max_length=50,
                    ),
                ),
                ("context_data", models.J<PERSON><PERSON>ield(default=dict)),
                ("messages", models.JSONField(default=list)),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name": "AI Conversation",
                "verbose_name_plural": "AI Conversations",
                "db_table": "ai_service_conversation",
            },
        ),
        migrations.CreateModel(
            name="AIDecision",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "decision_type",
                    models.CharField(
                        choices=[
                            ("TASK_ASSIGNMENT", "Task Assignment"),
                            ("PRIORITY_SETTING", "Priority Setting"),
                            ("RESOURCE_ALLOCATION", "Resource Allocation"),
                            ("TIMELINE_ESTIMATION", "Timeline Estimation"),
                            ("HEADCOUNT_REQUEST", "Headcount Request"),
                            ("WORKFLOW_TRANSITION", "Workflow Transition"),
                        ],
                        max_length=50,
                    ),
                ),
                ("decision_data", models.JSONField(default=dict)),
                ("reasoning", models.TextField()),
                (
                    "confidence_score",
                    models.DecimalField(decimal_places=2, max_digits=3),
                ),
                ("alternatives_considered", models.JSONField(default=list)),
                ("outcome_data", models.JSONField(blank=True, default=dict, null=True)),
                (
                    "feedback_score",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=3, null=True
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name": "AI Decision",
                "verbose_name_plural": "AI Decisions",
                "db_table": "ai_service_decision",
            },
        ),
        migrations.CreateModel(
            name="AIMetrics",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "metric_type",
                    models.CharField(
                        choices=[
                            ("ACCURACY", "Decision Accuracy"),
                            ("EFFICIENCY", "Task Efficiency"),
                            ("SATISFACTION", "User Satisfaction"),
                            ("COMPLETION_RATE", "Task Completion Rate"),
                            ("ESTIMATION_ACCURACY", "Time Estimation Accuracy"),
                        ],
                        max_length=50,
                    ),
                ),
                ("metric_value", models.DecimalField(decimal_places=4, max_digits=10)),
                ("metric_data", models.JSONField(default=dict)),
                ("measurement_period_start", models.DateTimeField()),
                ("measurement_period_end", models.DateTimeField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
            ],
            options={
                "verbose_name": "AI Metrics",
                "verbose_name_plural": "AI Metrics",
                "db_table": "ai_service_metrics",
            },
        ),
        migrations.CreateModel(
            name="AIPromptTemplate",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("name", models.CharField(max_length=100)),
                (
                    "template_type",
                    models.CharField(
                        choices=[
                            ("SYSTEM", "System Prompt"),
                            ("TASK_ANALYSIS", "Task Analysis"),
                            ("REQUIREMENT_REVIEW", "Requirement Review"),
                            ("TEAM_ASSESSMENT", "Team Assessment"),
                            ("PROGRESS_UPDATE", "Progress Update"),
                        ],
                        max_length=50,
                    ),
                ),
                (
                    "role_specific",
                    models.CharField(blank=True, max_length=50, null=True),
                ),
                ("template_content", models.TextField()),
                ("variables", models.JSONField(default=list)),
                ("version", models.CharField(default="1.0", max_length=20)),
                ("is_active", models.BooleanField(default=True)),
                ("usage_count", models.IntegerField(default=0)),
                (
                    "success_rate",
                    models.DecimalField(decimal_places=2, default=0.0, max_digits=5),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name": "AI Prompt Template",
                "verbose_name_plural": "AI Prompt Templates",
                "db_table": "ai_service_prompt_template",
            },
        ),
    ]
