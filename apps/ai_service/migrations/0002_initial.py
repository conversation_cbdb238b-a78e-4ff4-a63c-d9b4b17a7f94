# Generated by Django 5.2.4 on 2025-07-06 10:42

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("ai_service", "0001_initial"),
        ("projects", "0001_initial"),
        ("team", "0001_initial"),
    ]

    operations = [
        migrations.AddField(
            model_name="aiconversation",
            name="project",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="projects.project",
            ),
        ),
        migrations.AddField(
            model_name="aiconversation",
            name="team_member",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="team.teammember",
            ),
        ),
        migrations.AddField(
            model_name="aidecision",
            name="conversation",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="ai_service.aiconversation",
            ),
        ),
        migrations.AddField(
            model_name="aimetrics",
            name="project",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="projects.project",
            ),
        ),
        migrations.AddField(
            model_name="aimetrics",
            name="team_member",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="team.teammember",
            ),
        ),
        migrations.AddIndex(
            model_name="aiprompttemplate",
            index=models.Index(
                fields=["template_type", "is_active"],
                name="ai_service__templat_c439ea_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="aiprompttemplate",
            index=models.Index(
                fields=["role_specific", "is_active"],
                name="ai_service__role_sp_d2c303_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="aiprompttemplate",
            index=models.Index(
                fields=["success_rate"], name="ai_service__success_f50d9c_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="aiconversation",
            index=models.Index(
                fields=["project", "conversation_type"],
                name="ai_service__project_b0186d_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="aiconversation",
            index=models.Index(
                fields=["team_member", "is_active"],
                name="ai_service__team_me_3d1ccb_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="aiconversation",
            index=models.Index(
                fields=["created_at"], name="ai_service__created_6c1312_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="aidecision",
            index=models.Index(
                fields=["decision_type", "confidence_score"],
                name="ai_service__decisio_aa98fa_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="aidecision",
            index=models.Index(
                fields=["created_at"], name="ai_service__created_36d7e4_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="aidecision",
            index=models.Index(
                fields=["feedback_score"], name="ai_service__feedbac_0670c1_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="aimetrics",
            index=models.Index(
                fields=["team_member", "metric_type"],
                name="ai_service__team_me_c8d614_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="aimetrics",
            index=models.Index(
                fields=["project", "metric_type"], name="ai_service__project_e8e5cb_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="aimetrics",
            index=models.Index(
                fields=["measurement_period_start", "measurement_period_end"],
                name="ai_service__measure_2b43f1_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="aimetrics",
            index=models.Index(
                fields=["created_at"], name="ai_service__created_ddc009_idx"
            ),
        ),
    ]
