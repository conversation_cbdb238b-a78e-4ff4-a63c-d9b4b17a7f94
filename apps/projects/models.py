import uuid
from django.db import models
from django.conf import settings
from apps.team.models import <PERSON><PERSON>ember, AIRole


class Project(models.Model):
    """
    Represents a project with requirements, AI analysis, and team assignments.
    """
    PRIORITY_CHOICES = [
        ('LOW', 'Low'),
        ('MEDIUM', 'Medium'),
        ('HIGH', 'High'),
        ('CRITICAL', 'Critical'),
    ]

    STATUS_CHOICES = [
        ('PLANNING', 'Planning'),
        ('IN_PROGRESS', 'In Progress'),
        ('ON_HOLD', 'On Hold'),
        ('COMPLETED', 'Completed'),
        ('CANCELLED', 'Cancelled'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    owner = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    title = models.CharField(max_length=200)
    description = models.TextField()
    requirements = models.TextField()  # Original user requirements
    ai_analysis = models.J<PERSON><PERSON><PERSON>(null=True, blank=True)  # AI PM analysis results
    priority = models.CharField(max_length=20, choices=PRIORITY_CHOICES)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='PLANNING')
    estimated_duration = models.DurationField(null=True, blank=True)
    actual_duration = models.DurationField(null=True, blank=True)
    budget = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    start_date = models.DateTimeField(null=True, blank=True)
    due_date = models.DateTimeField(null=True, blank=True)
    completion_date = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'projects_project'
        verbose_name = 'Project'
        verbose_name_plural = 'Projects'
        indexes = [
            models.Index(fields=['owner', 'status']),
            models.Index(fields=['priority', 'status']),
            models.Index(fields=['due_date']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return self.title

    @property
    def progress_percentage(self):
        """Calculate project progress based on completed tasks."""
        total_tasks = self.task_set.count()
        if total_tasks == 0:
            return 0
        completed_tasks = self.task_set.filter(status='COMPLETED').count()
        return (completed_tasks / total_tasks) * 100


class ProjectTeamMember(models.Model):
    """
    Represents the assignment of team members to projects with their roles and allocation.
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    project = models.ForeignKey(Project, on_delete=models.CASCADE)
    team_member = models.ForeignKey(TeamMember, on_delete=models.CASCADE)
    role_in_project = models.CharField(max_length=50)
    allocation_percentage = models.DecimalField(max_digits=5, decimal_places=2)
    joined_at = models.DateTimeField(auto_now_add=True)
    left_at = models.DateTimeField(null=True, blank=True)
    is_active = models.BooleanField(default=True)

    class Meta:
        db_table = 'projects_team_member'
        verbose_name = 'Project Team Member'
        verbose_name_plural = 'Project Team Members'
        unique_together = ['project', 'team_member']
        indexes = [
            models.Index(fields=['project', 'is_active']),
            models.Index(fields=['team_member', 'is_active']),
        ]

    def __str__(self):
        return f"{self.team_member.name} on {self.project.title}"


class HeadcountRequest(models.Model):
    """
    Represents AI-generated requests for additional team members.
    """
    URGENCY_CHOICES = [
        ('LOW', 'Low'),
        ('MEDIUM', 'Medium'),
        ('HIGH', 'High'),
        ('CRITICAL', 'Critical'),
    ]

    REQUEST_STATUS_CHOICES = [
        ('PENDING', 'Pending Review'),
        ('APPROVED', 'Approved'),
        ('REJECTED', 'Rejected'),
        ('FULFILLED', 'Fulfilled'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    project = models.ForeignKey(Project, on_delete=models.CASCADE)
    requested_by = models.CharField(max_length=50, default='AI_PM')
    ai_role = models.ForeignKey(AIRole, on_delete=models.CASCADE)
    justification = models.TextField()  # AI-generated reasoning
    urgency = models.CharField(max_length=20, choices=URGENCY_CHOICES)
    required_skills = models.JSONField(default=list)
    estimated_duration = models.DurationField()
    budget_impact = models.DecimalField(max_digits=10, decimal_places=2)
    status = models.CharField(max_length=20, choices=REQUEST_STATUS_CHOICES, default='PENDING')
    ceo_response = models.TextField(null=True, blank=True)
    requested_at = models.DateTimeField(auto_now_add=True)
    reviewed_at = models.DateTimeField(null=True, blank=True)
    approved_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        db_table = 'projects_headcount_request'
        verbose_name = 'Headcount Request'
        verbose_name_plural = 'Headcount Requests'
        indexes = [
            models.Index(fields=['project', 'status']),
            models.Index(fields=['status', 'urgency']),
            models.Index(fields=['requested_at']),
        ]

    def __str__(self):
        return f"Request for {self.ai_role.name} on {self.project.title}"
