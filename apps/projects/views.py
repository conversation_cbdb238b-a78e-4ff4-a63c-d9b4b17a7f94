from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
from django.utils import timezone
from .models import Project, ProjectTeamMember, HeadcountRequest
from .serializers import (
    ProjectListSerializer, ProjectDetailSerializer, ProjectCreateSerializer,
    ProjectTeamMemberSerializer, HeadcountRequestSerializer, HeadcountRequestApprovalSerializer
)
from apps.team.models import TeamMember


class ProjectViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing projects with CRUD operations and custom actions.
    """
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return Project.objects.filter(owner=self.request.user).order_by('-created_at')

    def get_serializer_class(self):
        if self.action == 'list':
            return ProjectListSerializer
        elif self.action == 'create':
            return ProjectCreateSerializer
        return ProjectDetailSerializer

    @action(detail=True, methods=['post'])
    def add_team_member(self, request, pk=None):
        """
        Add a team member to the project.
        """
        project = self.get_object()
        serializer = ProjectTeamMemberSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save(project=project)
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['delete'])
    def remove_team_member(self, request, pk=None):
        """
        Remove a team member from the project.
        """
        project = self.get_object()
        team_member_id = request.data.get('team_member_id')
        if not team_member_id:
            return Response({'error': 'team_member_id is required'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            ptm = ProjectTeamMember.objects.get(project=project, team_member_id=team_member_id)
            ptm.is_active = False
            ptm.left_at = timezone.now()
            ptm.save()
            return Response({'message': 'Team member removed successfully'}, status=status.HTTP_200_OK)
        except ProjectTeamMember.DoesNotExist:
            return Response({'error': 'Team member not found in project'}, status=status.HTTP_404_NOT_FOUND)

    @action(detail=True, methods=['get'])
    def team_members(self, request, pk=None):
        """
        Get all team members for the project.
        """
        project = self.get_object()
        team_members = ProjectTeamMember.objects.filter(project=project, is_active=True)
        serializer = ProjectTeamMemberSerializer(team_members, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def update_status(self, request, pk=None):
        """
        Update project status.
        """
        project = self.get_object()
        new_status = request.data.get('status')
        if new_status not in dict(Project.STATUS_CHOICES):
            return Response({'error': 'Invalid status'}, status=status.HTTP_400_BAD_REQUEST)

        project.status = new_status
        if new_status == 'COMPLETED':
            project.completion_date = timezone.now()
        project.save()

        serializer = ProjectDetailSerializer(project)
        return Response(serializer.data)


class HeadcountRequestViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing headcount requests.
    """
    serializer_class = HeadcountRequestSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return HeadcountRequest.objects.filter(
            project__owner=self.request.user
        ).order_by('-requested_at')

    @action(detail=True, methods=['post'])
    def approve(self, request, pk=None):
        """
        Approve or reject a headcount request.
        """
        headcount_request = self.get_object()
        serializer = HeadcountRequestApprovalSerializer(data=request.data)

        if serializer.is_valid():
            approved = serializer.validated_data['approved']
            ceo_response = serializer.validated_data['ceo_response']

            headcount_request.status = 'APPROVED' if approved else 'REJECTED'
            headcount_request.ceo_response = ceo_response
            headcount_request.reviewed_at = timezone.now()

            if approved:
                headcount_request.approved_at = timezone.now()
                # Create new team member if approved
                new_team_member = TeamMember.objects.create(
                    ai_role=headcount_request.ai_role,
                    name=f"AI {headcount_request.ai_role.name}",
                    specialization=headcount_request.ai_role.name,
                    skills=headcount_request.required_skills
                )

                # Add to project team
                ProjectTeamMember.objects.create(
                    project=headcount_request.project,
                    team_member=new_team_member,
                    role_in_project=headcount_request.ai_role.name,
                    allocation_percentage=100.0
                )

                headcount_request.status = 'FULFILLED'

            headcount_request.save()

            response_data = HeadcountRequestSerializer(headcount_request).data
            if approved:
                response_data['new_team_member'] = {
                    'id': new_team_member.id,
                    'name': new_team_member.name,
                    'hire_date': new_team_member.hire_date
                }

            return Response(response_data, status=status.HTTP_200_OK)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
