# Generated by Django 5.2.4 on 2025-07-06 10:42

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("team", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Project",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("title", models.CharField(max_length=200)),
                ("description", models.TextField()),
                ("requirements", models.TextField()),
                ("ai_analysis", models.J<PERSON>NField(blank=True, null=True)),
                (
                    "priority",
                    models.CharField(
                        choices=[
                            ("LOW", "Low"),
                            ("MEDIUM", "Medium"),
                            ("HIGH", "High"),
                            ("CRITICAL", "Critical"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "status",
                    models.Char<PERSON>ield(
                        choices=[
                            ("PLANNING", "Planning"),
                            ("IN_PROGRESS", "In Progress"),
                            ("ON_HOLD", "On Hold"),
                            ("COMPLETED", "Completed"),
                            ("CANCELLED", "Cancelled"),
                        ],
                        default="PLANNING",
                        max_length=20,
                    ),
                ),
                ("estimated_duration", models.DurationField(blank=True, null=True)),
                ("actual_duration", models.DurationField(blank=True, null=True)),
                (
                    "budget",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=10, null=True
                    ),
                ),
                ("start_date", models.DateTimeField(blank=True, null=True)),
                ("due_date", models.DateTimeField(blank=True, null=True)),
                ("completion_date", models.DateTimeField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "owner",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Project",
                "verbose_name_plural": "Projects",
                "db_table": "projects_project",
            },
        ),
        migrations.CreateModel(
            name="HeadcountRequest",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("requested_by", models.CharField(default="AI_PM", max_length=50)),
                ("justification", models.TextField()),
                (
                    "urgency",
                    models.CharField(
                        choices=[
                            ("LOW", "Low"),
                            ("MEDIUM", "Medium"),
                            ("HIGH", "High"),
                            ("CRITICAL", "Critical"),
                        ],
                        max_length=20,
                    ),
                ),
                ("required_skills", models.JSONField(default=list)),
                ("estimated_duration", models.DurationField()),
                ("budget_impact", models.DecimalField(decimal_places=2, max_digits=10)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("PENDING", "Pending Review"),
                            ("APPROVED", "Approved"),
                            ("REJECTED", "Rejected"),
                            ("FULFILLED", "Fulfilled"),
                        ],
                        default="PENDING",
                        max_length=20,
                    ),
                ),
                ("ceo_response", models.TextField(blank=True, null=True)),
                ("requested_at", models.DateTimeField(auto_now_add=True)),
                ("reviewed_at", models.DateTimeField(blank=True, null=True)),
                ("approved_at", models.DateTimeField(blank=True, null=True)),
                (
                    "ai_role",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="team.airole"
                    ),
                ),
                (
                    "project",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="projects.project",
                    ),
                ),
            ],
            options={
                "verbose_name": "Headcount Request",
                "verbose_name_plural": "Headcount Requests",
                "db_table": "projects_headcount_request",
            },
        ),
        migrations.CreateModel(
            name="ProjectTeamMember",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("role_in_project", models.CharField(max_length=50)),
                (
                    "allocation_percentage",
                    models.DecimalField(decimal_places=2, max_digits=5),
                ),
                ("joined_at", models.DateTimeField(auto_now_add=True)),
                ("left_at", models.DateTimeField(blank=True, null=True)),
                ("is_active", models.BooleanField(default=True)),
                (
                    "project",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="projects.project",
                    ),
                ),
                (
                    "team_member",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="team.teammember",
                    ),
                ),
            ],
            options={
                "verbose_name": "Project Team Member",
                "verbose_name_plural": "Project Team Members",
                "db_table": "projects_team_member",
            },
        ),
        migrations.AddIndex(
            model_name="project",
            index=models.Index(
                fields=["owner", "status"], name="projects_pr_owner_i_4798b7_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="project",
            index=models.Index(
                fields=["priority", "status"], name="projects_pr_priorit_17637b_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="project",
            index=models.Index(
                fields=["due_date"], name="projects_pr_due_dat_c36735_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="project",
            index=models.Index(
                fields=["created_at"], name="projects_pr_created_6b02e3_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="headcountrequest",
            index=models.Index(
                fields=["project", "status"], name="projects_he_project_26f54a_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="headcountrequest",
            index=models.Index(
                fields=["status", "urgency"], name="projects_he_status_e34216_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="headcountrequest",
            index=models.Index(
                fields=["requested_at"], name="projects_he_request_52ece2_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="projectteammember",
            index=models.Index(
                fields=["project", "is_active"], name="projects_te_project_b5143c_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="projectteammember",
            index=models.Index(
                fields=["team_member", "is_active"],
                name="projects_te_team_me_75d814_idx",
            ),
        ),
        migrations.AlterUniqueTogether(
            name="projectteammember",
            unique_together={("project", "team_member")},
        ),
    ]
