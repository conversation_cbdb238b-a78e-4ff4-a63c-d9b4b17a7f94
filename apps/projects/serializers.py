from rest_framework import serializers
from .models import Project, ProjectTeamMember, HeadcountRequest
from apps.team.models import <PERSON><PERSON>ember, AIRole
from apps.authentication.serializers import UserSerializer


class ProjectListSerializer(serializers.ModelSerializer):
    """
    Serializer for project list view with summary information.
    """
    owner = UserSerializer(read_only=True)
    progress_percentage = serializers.ReadOnlyField()
    team_size = serializers.SerializerMethodField()

    class Meta:
        model = Project
        fields = [
            'id', 'title', 'description', 'status', 'priority', 
            'estimated_duration', 'progress_percentage', 'team_size',
            'owner', 'created_at', 'due_date'
        ]

    def get_team_size(self, obj):
        return obj.projectteammember_set.filter(is_active=True).count()


class ProjectDetailSerializer(serializers.ModelSerializer):
    """
    Serializer for detailed project view with full information.
    """
    owner = UserSerializer(read_only=True)
    progress_percentage = serializers.ReadOnlyField()
    team_members = serializers.SerializerMethodField()
    tasks_summary = serializers.SerializerMethodField()

    class Meta:
        model = Project
        fields = [
            'id', 'title', 'description', 'requirements', 'ai_analysis',
            'priority', 'status', 'estimated_duration', 'actual_duration',
            'budget', 'start_date', 'due_date', 'completion_date',
            'progress_percentage', 'team_members', 'tasks_summary',
            'owner', 'created_at', 'updated_at'
        ]

    def get_team_members(self, obj):
        team_members = obj.projectteammember_set.filter(is_active=True).select_related('team_member', 'team_member__ai_role')
        return [{
            'id': ptm.team_member.id,
            'name': ptm.team_member.name,
            'role': ptm.team_member.ai_role.name,
            'allocation_percentage': ptm.allocation_percentage,
            'role_in_project': ptm.role_in_project
        } for ptm in team_members]

    def get_tasks_summary(self, obj):
        tasks = obj.task_set.all()
        return {
            'total': tasks.count(),
            'completed': tasks.filter(status='COMPLETED').count(),
            'in_progress': tasks.filter(status='IN_PROGRESS').count(),
            'todo': tasks.filter(status='TODO').count(),
            'blocked': tasks.filter(status='BLOCKED').count(),
        }


class ProjectCreateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating new projects.
    """
    class Meta:
        model = Project
        fields = [
            'title', 'description', 'requirements', 'priority', 'due_date', 'budget'
        ]

    def create(self, validated_data):
        validated_data['owner'] = self.context['request'].user
        return super().create(validated_data)


class ProjectTeamMemberSerializer(serializers.ModelSerializer):
    """
    Serializer for project team member assignments.
    """
    team_member_name = serializers.CharField(source='team_member.name', read_only=True)
    team_member_role = serializers.CharField(source='team_member.ai_role.name', read_only=True)

    class Meta:
        model = ProjectTeamMember
        fields = [
            'id', 'team_member', 'team_member_name', 'team_member_role',
            'role_in_project', 'allocation_percentage', 'joined_at', 'is_active'
        ]


class HeadcountRequestSerializer(serializers.ModelSerializer):
    """
    Serializer for headcount requests.
    """
    project_title = serializers.CharField(source='project.title', read_only=True)
    ai_role_name = serializers.CharField(source='ai_role.name', read_only=True)
    ai_role_experience = serializers.CharField(source='ai_role.experience_level', read_only=True)

    class Meta:
        model = HeadcountRequest
        fields = [
            'id', 'project', 'project_title', 'ai_role', 'ai_role_name', 
            'ai_role_experience', 'justification', 'urgency', 'required_skills',
            'estimated_duration', 'budget_impact', 'status', 'ceo_response',
            'requested_by', 'requested_at', 'reviewed_at', 'approved_at'
        ]
        read_only_fields = ['requested_by', 'requested_at']


class HeadcountRequestApprovalSerializer(serializers.Serializer):
    """
    Serializer for approving/rejecting headcount requests.
    """
    approved = serializers.BooleanField()
    ceo_response = serializers.CharField(max_length=1000)

    def validate(self, attrs):
        if not attrs.get('ceo_response'):
            raise serializers.ValidationError("CEO response is required.")
        return attrs
