../../../bin/black,sha256=ldRLbeO-qjXJGTzIUjU11b49uy4e6_XyAzjMCPJa9mA,361
../../../bin/blackd,sha256=P4iIL6__GIbJy-1KDWbuNm0vnIS24helso-BW4xyJLo,362
30fcd23745efe32ce681__mypyc.cpython-311-darwin.so,sha256=oi755wgw7RnVkvqyTgBHGkbUEs28DCz2zzB6ifxbCqM,4007216
_black_version.py,sha256=FhAjEtEWGP4vzVPv6krA2rMrSoPtKcVJ74gjENa73Zo,19
black-25.1.0.dist-info/INSTALLER,sha256=5hhM4Q4mYTT9z6QB6PGpUAW81PGNFrYrdXMj4oM_6ak,2
black-25.1.0.dist-info/METADATA,sha256=oSdftyY9ijULKJlSA4hI4IOzomCceuRzrWPylASmVac,81269
black-25.1.0.dist-info/RECORD,,
black-25.1.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
black-25.1.0.dist-info/WHEEL,sha256=tZH-T5779LoPeRTRIKnBcnvP6tCfHMVLqaisf4Q1fsw,106
black-25.1.0.dist-info/entry_points.txt,sha256=XTCA4X2yVA0tMiV7l96Gv9TyxhVhoCaznLN2XThqYSA,144
black-25.1.0.dist-info/licenses/AUTHORS.md,sha256=q4LhA36Sf7X6e5xzVq7dFClyVKdxK2z7gpos_YsGrIg,8149
black-25.1.0.dist-info/licenses/LICENSE,sha256=nAQo8MO0d5hQz1vZbhGqqK_HLUqG1KNiI9erouWNbgA,1080
black/__init__.cpython-311-darwin.so,sha256=-EQkRTV6K6Sy_Eoh_VNucm0rG-5yPkkS5dISZMaTZW0,50144
black/__init__.py,sha256=M4-GQzsO9_OdhW0EofQrkW3n5Z1xUQ__kqjUfiAO15Y,51644
black/__main__.py,sha256=mogeA4o9zt4w-ufKvaQjSEhtSgQkcMVLK9ChvdB5wH8,47
black/_width_table.cpython-311-darwin.so,sha256=f1bTRzGOqzuXabQpnrNSwtaENQBFW5yQiqAnTUqsVYE,50160
black/_width_table.py,sha256=3qJd3E9YehKhRowZzBOfTv5Uv9gnFX-cAi4ydwxu0q0,10748
black/brackets.cpython-311-darwin.so,sha256=80oMWH5xHFAOB_5Mw6LHar0mN85G5QXyRbxrpVVwCaI,50144
black/brackets.py,sha256=nSMRUC9-WxZ6xIAlCGkvl5MQYbldJj4fQiGzi-QMSq4,12429
black/cache.cpython-311-darwin.so,sha256=iF9rFz3jSZoVP_MQr73pNFTuRWbYOmY0GRrpvS7KmwU,50136
black/cache.py,sha256=_N51IHzj0-D55kDuk8v9hm0TfKfsJv1bQL3anxUR4k4,4754
black/comments.cpython-311-darwin.so,sha256=GMI3VXRevXIO9ImQL_3_xjlsH0M2tXrmdV8UpVocgu0,50144
black/comments.py,sha256=Bi72oBehZOVkyoo_WSTO0hnRFRP2--LmpUstmIlux6o,15818
black/concurrency.py,sha256=nsQKuu_ZMeaWi-k3E-HTqjTDlwLV6k92vOaJgjskWqw,6432
black/const.cpython-311-darwin.so,sha256=Ch-NMk8E8l1flFZxAz3BeiNJFNSF72WtfaXlnHqcd6k,50136
black/const.py,sha256=U7cDnhWljmrieOtPBUdO2Vcz69J_VXB6-Br94wuCVuo,321
black/debug.py,sha256=yJBVRbD-jYgPK-tKksgcHUgmjqU9ggBfyve5hQSLlEg,1927
black/files.py,sha256=xaBMK-pvfqORqMFik-CC7Eaeq_51xyyhJ3_ENWBrdiY,14722
black/handle_ipynb_magics.cpython-311-darwin.so,sha256=4-5RKTY2FbQsQbLx64vITxPjO7qXBGQjoNuYWhjNlDA,50184
black/handle_ipynb_magics.py,sha256=1s9RD59lOgS71fOgE8bEXJiPeUdEouUYPTu6_wv5f6c,15494
black/linegen.cpython-311-darwin.so,sha256=EUwaKosiu-3GDaJS7pS0KQzJBp8Kspgm3YX0Cpx0hkI,50144
black/linegen.py,sha256=TdJ7AVf7YyqERXZOkNZJ1cllGUQ0nxDI2iRZUcZJpgM,70488
black/lines.cpython-311-darwin.so,sha256=xWyVyTX0rSxtotLYv95bvv2EzWSCFLlKgDfNBbRIyIU,50136
black/lines.py,sha256=hC1td-dENO_4QoTNY78GP8DME2cZh1i_O-BrN-BL2ho,39620
black/mode.cpython-311-darwin.so,sha256=2wyymfbaPpvkNzrGEXen86UytxARY8kEwAahocU-VQo,50136
black/mode.py,sha256=y1_iRcvfCVxmSvFbnNsMTUXXocBZrLaOUZq_w8SqBW0,9065
black/nodes.cpython-311-darwin.so,sha256=rAGYIJiYxMWKc1U0I-C6ULTwq5-y4DDYKWIGsmjNB80,50136
black/nodes.py,sha256=XFNkJEyZFMZq0R0bPn_cT7fEhy6vSu2qoZGELChUMS4,30418
black/numerics.cpython-311-darwin.so,sha256=TGEpQCW9sQkU4oLXDTcfm51TffNprd5KX-VepVOvdYc,50144
black/numerics.py,sha256=xRGnTSdMVbTaA9IGechc8JM-cIuJGCc326s71hkXJIw,1655
black/output.py,sha256=z8vs-bWADomxHav4sy_YpX_QpauGFp5SXr3Gj7kQDKI,3933
black/parsing.cpython-311-darwin.so,sha256=Y3qH303fKXmp0Sj2lkrDZDWu6eYDdrYwbXU1YgOV1DQ,50144
black/parsing.py,sha256=eyf1PGJZ6MKV7lky37m-RmTLxUL2ggcvffqjxi0meRA,8621
black/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
black/ranges.cpython-311-darwin.so,sha256=_nKQ74-OxcuK7cD6bZtE0ZbFEil9kZp8pgczw2Ms64A,50144
black/ranges.py,sha256=aegh-sCgti-okdOWd-0o9UZFyh5BMoBuxg-n80MehNc,19704
black/report.py,sha256=igkNi8iR5FqSa1sXddS-HnoqW7Cq7PveCmFCfd-pN6w,3452
black/resources/__init__.cpython-311-darwin.so,sha256=RUFcAIFQiR927Taz0rms_dKknxa99vju_xQPw_6yar0,50160
black/resources/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
black/resources/black.schema.json,sha256=PL61UkUw7DSl2OrC6BqBM1wXu5O6IzUSUdDdU_QZWGM,7137
black/rusty.cpython-311-darwin.so,sha256=WA3Xots1FCMOunlqkHjtuIXvhGPCZMHWP4dM6npm3Ks,50136
black/rusty.py,sha256=4LKo3KTUWYZ2cN6QKmwwZVbsCNt2fpu5lzue2V-uxIA,557
black/schema.cpython-311-darwin.so,sha256=jUNnQWBfV_qLy09SMrJnJ1tYYrv6xw15VA39FP8FoTE,50144
black/schema.py,sha256=ru0z9EA-f3wfLLVPefeFpM0s4GYsYg_UjpOiMLhjdbA,431
black/strings.cpython-311-darwin.so,sha256=385h-IOao3qR3FIEvKhhW9-oTiyx_nm04hGMx9qnpYk,50144
black/strings.py,sha256=VQ04cvueKygPkmNFeumBvxdJX2GE-XlYby7bXGoPthI,13220
black/trans.cpython-311-darwin.so,sha256=8uvls96QhiGyCYo38QgtiJHlzVj3MXa7LVGwQpfsTxw,50136
black/trans.py,sha256=xrb16nZMFB9SstT4kCE6HQN_mOOEQh3IcKL_iS3Jj14,95191
blackd/__init__.py,sha256=V9-BiApAg1drY7fajHoAYAVPthmA7BzZNLhmTjLJ0Kc,8879
blackd/__main__.py,sha256=L4xAcDh1K5zb6SsJB102AewW2G13P9-w2RiEwuFj8WA,37
blackd/middlewares.py,sha256=kZZavG9kvAbXKrlwIQCnDqK6fZ9FyAYzdKwqp_IcHpg,1172
blib2to3/Grammar.txt,sha256=zjM1rSC9GJjnboYyRDZyKx2IPWDkscVdodwQpDCs4So,11700
blib2to3/LICENSE,sha256=V4mIG4rrnJH1g19bt8q-hKD-zUuyvi9UyeaVenjseZ0,12762
blib2to3/PatternGrammar.txt,sha256=7lul2ztnIqDi--JWDrwciD5yMo75w7TaHHxdHMZJvOM,793
blib2to3/README,sha256=QYZYIfb1NXTTYqDV4kn8oRcNG_qlTFYH1sr3V1h65ko,1074
blib2to3/__init__.py,sha256=9_8wL9Scv8_Cs8HJyJHGvx1vwXErsuvlsAqNZLcJQR0,8
blib2to3/pgen2/__init__.py,sha256=hY6w9QUzvTvRb-MoFfd_q_7ZLt6IUHC2yxWCfsZupQA,143
blib2to3/pgen2/conv.cpython-311-darwin.so,sha256=R41bqKt3qdLtqQlrVWygAuJRMoBYFsm75Ra21iM5QHM,50136
blib2to3/pgen2/conv.py,sha256=vH8a_gkalWRNxuNPRxkoigw8_UobdHHSw-PyUcUuH8I,9587
blib2to3/pgen2/driver.cpython-311-darwin.so,sha256=o0UXDw0icrxUPef-OEU2jhqE7GzIweM06hOQT2KgDjQ,50144
blib2to3/pgen2/driver.py,sha256=zoEqEI_Z0SYlJqphc2E7CFvHwrlSNv9yscATAJ6M87c,10846
blib2to3/pgen2/grammar.cpython-311-darwin.so,sha256=Wcx5EzUtwK1QEPK_vU6e8uD82Mhi67wYhvoupEKvjtw,50144
blib2to3/pgen2/grammar.py,sha256=xApSZeigr9IBog2G9_vLvhOiKqUjZrQOPHlrieCw8lE,6846
blib2to3/pgen2/literals.cpython-311-darwin.so,sha256=2xqjDLXogZrcbZKM9AAIarb8-UrNJ-Bp75A-WslA8_A,50144
blib2to3/pgen2/literals.py,sha256=i-Y8SlaJ7dU6jntWQm_g8GpG2zl1akZmZhsz1igdYR8,1586
blib2to3/pgen2/parse.cpython-311-darwin.so,sha256=Ej9DMdkklrdzxu4LzE6kHU5KgUUbiyR8Aj80odDCM70,50136
blib2to3/pgen2/parse.py,sha256=ILEYny98jrfODxMG3MADPBaWuaDu3wpfYLp5rrdV_jY,15612
blib2to3/pgen2/pgen.cpython-311-darwin.so,sha256=SizuilhKn2s2ga4JYTq3Z2xEHiaibxHjrh492Ilhi04,50136
blib2to3/pgen2/pgen.py,sha256=TT5etH65ltNwcJakhK50u3Z8ZiOo7Bj2CjbCnM7AiyU,15418
blib2to3/pgen2/token.cpython-311-darwin.so,sha256=wQzGy4AghZaaU8_6z6pl_6_rbjEQM2VPwoaVBIkwFrI,50136
blib2to3/pgen2/token.py,sha256=pb5cvttERocFGRWjJ5C1f_a0S4C_UKmTfHXMqyFKoig,1893
blib2to3/pgen2/tokenize.cpython-311-darwin.so,sha256=xD-757EnuSgdndFYzP0cMVbe8kezEbjFq0OPH7f-Ew4,50144
blib2to3/pgen2/tokenize.py,sha256=dc6fou2882mdBLHm6Ik-qsTkzux7T-iQAftPZs0V-2Q,41468
blib2to3/pygram.cpython-311-darwin.so,sha256=0zFyOkG-h-NanIkz5kR621slnVC1m4z9DpDh8AQWXlY,50144
blib2to3/pygram.py,sha256=l2qw7mw8I533KGWAXUFCXPGCN5F66hvYg86g-EA9GEg,4915
blib2to3/pytree.cpython-311-darwin.so,sha256=616l3m3isePphg3jL8HlVMiqKTzSFy49UaYFh1H9Yng,50144
blib2to3/pytree.py,sha256=52hBl0unVlUdec-LojHpey6j98Qcrrd_HXQSxTj2StY,32624
